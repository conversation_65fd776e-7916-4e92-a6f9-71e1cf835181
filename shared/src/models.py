from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional

class AgentStatus(Enum):
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class ToolResult:
    success: bool
    data: Any = None
    error: Optional[str] = None
    
    @classmethod
    def success_result(cls, data: Any) -> 'ToolResult':
        return cls(success=True, data=data)
        
    @classmethod
    def error_result(cls, error: str) -> 'ToolResult':
        return cls(success=False, error=error)

@dataclass
class AgentState:
    topic: str = ""
    status: AgentStatus = AgentStatus.IDLE
    research_data: Dict[str, Any] = field(default_factory=dict)
    final_output: Any = None
    error_log: List[str] = field(default_factory=list)
    
    def reset(self):
        self.status = AgentStatus.IDLE
        self.research_data = {}
        self.final_output = None
        self.error_log = []