import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # Main API Keys
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyBbjMtyjOP7_zmOHFisHGXd6HEQdjYAxwA")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")
    REPLICATE_API_TOKEN = os.getenv("REPLICATE_API_TOKEN", "492d74a4ce439b812a46e2be935132922791ca00")
    
    # Additional API Keys
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "************************************************************************************************************")
    GROQ_API_KEY = os.getenv("GROQ_API_KEY", "********************************************************")
    TOGETHER_API_KEY = os.getenv("TOGETHER_API_KEY", "07f6820eafb628892416280c09a57d0de4934bb5ac317ae733b1a939235165c3")
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "***********************************")
    COHERE_API_KEY = os.getenv("COHERE_API_KEY", "SDeCYngUTNbgyMlHrQhzAmo26BbdLJ5BfpDUhqg7")
    
    # Configuration
    AGENT_MODEL = os.getenv("AGENT_MODEL", "gemini-pro")
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
settings = Settings()