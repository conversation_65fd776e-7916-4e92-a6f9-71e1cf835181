import logging
from abc import ABC, abstractmethod
from typing import Any, Dict

class BaseTool(ABC):
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = logging.getLogger(f"tool.{name}")
        
    @abstractmethod
    async def execute(self, state, *args, **kwargs):
        pass
        
    def __str__(self):
        return f"{self.name}: {self.description}"