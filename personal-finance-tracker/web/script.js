// Personal Finance Tracker JavaScript
let transactionType = '';
let spendingChart;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    setTodayDate();
    
    // Initialize transaction form
    document.getElementById('transactionForm').addEventListener('submit', handleTransactionSubmit);
});

function initializeChart() {
    const ctx = document.getElementById('spendingChart').getContext('2d');
    
    spendingChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Expenses',
                data: [3200, 2800, 3400, 3100, 2900, 3140],
                borderColor: '#f5576c',
                backgroundColor: 'rgba(245, 87, 108, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }, {
                label: 'Income',
                data: [5200, 5200, 5200, 5200, 5200, 5200],
                borderColor: '#38f9d7',
                backgroundColor: 'rgba(56, 249, 215, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff',
                        font: {
                            family: 'Inter',
                            size: 12
                        }
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#94a3b8',
                        font: {
                            family: 'Inter'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#94a3b8',
                        font: {
                            family: 'Inter'
                        },
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 8
                }
            }
        }
    });
}

function openTransactionModal(type) {
    transactionType = type;
    const modal = document.getElementById('transactionModal');
    const modalTitle = document.getElementById('modalTitle');
    
    modalTitle.textContent = type === 'income' ? 'Add Income' : 'Add Expense';
    modal.style.display = 'block';
    
    // Focus on amount field
    setTimeout(() => {
        document.getElementById('amount').focus();
    }, 100);
}

function closeTransactionModal() {
    const modal = document.getElementById('transactionModal');
    modal.style.display = 'none';
    
    // Reset form
    document.getElementById('transactionForm').reset();
}

function setTodayDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date').value = today;
}

function handleTransactionSubmit(e) {
    e.preventDefault();
    
    const amount = parseFloat(document.getElementById('amount').value);
    const description = document.getElementById('description').value;
    const category = document.getElementById('category').value;
    const date = document.getElementById('date').value;
    
    // Create transaction object
    const transaction = {
        type: transactionType,
        amount: amount,
        description: description,
        category: category,
        date: date,
        timestamp: new Date().toISOString()
    };
    
    // Process transaction
    processTransaction(transaction);
    
    // Close modal
    closeTransactionModal();
    
    // Show success notification
    showNotification(`${transactionType === 'income' ? 'Income' : 'Expense'} added successfully!`, 'success');
}

function processTransaction(transaction) {
    // Update balance
    updateBalance(transaction);
    
    // Add to transaction list
    addTransactionToList(transaction);
    
    // Update categories if expense
    if (transaction.type === 'expense') {
        updateCategorySpending(transaction);
    }
    
    // Update chart data
    updateChartData(transaction);
}

function updateBalance(transaction) {
    const balanceElement = document.querySelector('.balance-amount.total');
    const currentBalance = parseFloat(balanceElement.textContent.replace(/[$,]/g, ''));
    
    let newBalance;
    if (transaction.type === 'income') {
        newBalance = currentBalance + transaction.amount;
        
        // Update monthly income
        const incomeElement = document.querySelector('.balance-amount.income');
        const currentIncome = parseFloat(incomeElement.textContent.replace(/[+$,]/g, ''));
        incomeElement.textContent = `+$${(currentIncome + transaction.amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`;
    } else {
        newBalance = currentBalance - transaction.amount;
        
        // Update monthly expenses
        const expenseElement = document.querySelector('.balance-amount.expense');
        const currentExpenses = parseFloat(expenseElement.textContent.replace(/[-$,]/g, ''));
        expenseElement.textContent = `-$${(currentExpenses + transaction.amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`;
    }
    
    balanceElement.textContent = `$${newBalance.toLocaleString('en-US', {minimumFractionDigits: 2})}`;
}

function addTransactionToList(transaction) {
    const transactionsList = document.querySelector('.transactions-list');
    const transactionItem = document.createElement('div');
    transactionItem.className = 'transaction-item';
    
    const icon = getCategoryIcon(transaction.category);
    const formattedDate = formatDate(transaction.date);
    const sign = transaction.type === 'income' ? '+' : '-';
    
    transactionItem.innerHTML = `
        <div class="transaction-icon ${transaction.type}">
            <i class="${icon}"></i>
        </div>
        <div class="transaction-details">
            <span class="transaction-name">${transaction.description}</span>
            <span class="transaction-date">${formattedDate}</span>
        </div>
        <div class="transaction-amount ${transaction.type}">${sign}$${transaction.amount.toFixed(2)}</div>
    `;
    
    // Add to top of list
    transactionsList.insertBefore(transactionItem, transactionsList.firstChild);
    
    // Remove last item if more than 5 transactions
    if (transactionsList.children.length > 5) {
        transactionsList.removeChild(transactionsList.lastChild);
    }
}

function updateCategorySpending(transaction) {
    const categoryItems = document.querySelectorAll('.category-item');
    
    categoryItems.forEach(item => {
        const categoryName = item.querySelector('.category-name').textContent.toLowerCase();
        const categoryKey = getCategoryKey(transaction.category);
        
        if (categoryName.includes(categoryKey)) {
            const amountElement = item.querySelector('.category-amount');
            const currentAmount = parseFloat(amountElement.textContent.replace(/[$,]/g, ''));
            const newAmount = currentAmount + transaction.amount;
            
            amountElement.textContent = `$${newAmount.toFixed(2)}`;
            
            // Update progress bar (assuming budget of $500 for demo)
            const progressBar = item.querySelector('.category-progress');
            const percentage = Math.min((newAmount / 500) * 100, 100);
            progressBar.style.width = `${percentage}%`;
        }
    });
}

function updateChartData(transaction) {
    // This would typically update the chart with new data
    // For demo purposes, we'll just refresh the chart
    const currentMonth = new Date().getMonth();
    const monthData = spendingChart.data.datasets[transaction.type === 'income' ? 1 : 0].data;
    
    if (monthData[currentMonth]) {
        monthData[currentMonth] += transaction.amount;
        spendingChart.update();
    }
}

function getCategoryIcon(category) {
    const icons = {
        food: 'fas fa-utensils',
        transport: 'fas fa-car',
        bills: 'fas fa-home',
        entertainment: 'fas fa-gamepad',
        shopping: 'fas fa-shopping-bag',
        health: 'fas fa-heartbeat',
        other: 'fas fa-question-circle'
    };
    
    return icons[category] || icons.other;
}

function getCategoryKey(category) {
    const keys = {
        food: 'food',
        transport: 'transportation',
        bills: 'bills',
        entertainment: 'entertainment',
        shopping: 'shopping',
        health: 'healthcare',
        other: 'other'
    };
    
    return keys[category] || 'other';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
        return 'Today, ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday, ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else {
        const diffTime = Math.abs(today - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return `${diffDays} days ago`;
    }
}

function analyzeFinances() {
    // Simulate AI analysis
    showNotification('Analyzing your finances with AI...', 'info');
    
    setTimeout(() => {
        // Add new insight
        const insightsContent = document.querySelector('.insights-content');
        const newInsight = document.createElement('div');
        newInsight.className = 'insight-item';
        newInsight.innerHTML = `
            <i class="fas fa-chart-line insight-icon info"></i>
            <div class="insight-text">
                <strong>New Analysis:</strong> Based on your recent spending, consider setting aside $200 more for your emergency fund this month.
            </div>
        `;
        
        // Add to top of insights
        insightsContent.insertBefore(newInsight, insightsContent.firstChild);
        
        // Remove last insight if more than 3
        if (insightsContent.children.length > 3) {
            insightsContent.removeChild(insightsContent.lastChild);
        }
        
        showNotification('Financial analysis complete! Check your new insights.', 'success');
    }, 2000);
}

function exportData() {
    // Simulate data export
    showNotification('Preparing your financial data for export...', 'info');
    
    setTimeout(() => {
        // Create mock CSV data
        const csvData = `Date,Description,Category,Amount,Type
${new Date().toISOString().split('T')[0]},Sample Export,Other,0.00,expense`;
        
        // Create and download file
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'financial_data.csv';
        a.click();
        window.URL.revokeObjectURL(url);
        
        showNotification('Data exported successfully! 📊', 'success');
    }, 1500);
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    const colors = {
        success: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        error: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        info: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    };
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        z-index: 1001;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        backdrop-filter: blur(20px);
        animation: slideIn 0.3s ease;
        background: ${colors[type]};
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Close modal when clicking outside of it
window.onclick = function(event) {
    const modal = document.getElementById('transactionModal');
    if (event.target === modal) {
        closeTransactionModal();
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);