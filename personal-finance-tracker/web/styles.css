@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --warning-gradient: linear-gradient(135deg, #f7b733 0%, #fc4a1a 100%);
    --dark-bg: #0a0f1c;
    --card-bg: rgba(255, 255, 255, 0.08);
    --text-primary: #ffffff;
    --text-secondary: #94a3b8;
    --border-color: rgba(255, 255, 255, 0.12);
    --income-color: #38f9d7;
    --expense-color: #f5576c;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 25% 25%, rgba(17, 153, 142, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(56, 239, 125, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    z-index: -1;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.header {
    text-align: center;
    margin-bottom: 3rem;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.logo i {
    font-size: 3rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    font-size: 3rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    font-size: 1.25rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    grid-auto-rows: min-content;
}

.card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    margin-bottom: 1.5rem;
}

.card-header h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Balance Card */
.balance-card {
    grid-column: span 2;
}

.balance-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.balance-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid var(--border-color);
}

.balance-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.balance-amount {
    font-size: 2rem;
    font-weight: 700;
}

.balance-amount.total {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.balance-amount.income {
    color: var(--income-color);
}

.balance-amount.expense {
    color: var(--expense-color);
}

/* Actions Card */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.action-btn {
    padding: 1.25rem;
    border: none;
    border-radius: 15px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.action-btn i {
    font-size: 1.5rem;
}

.income-btn {
    background: var(--success-gradient);
}

.expense-btn {
    background: var(--accent-gradient);
}

.analyze-btn {
    background: var(--secondary-gradient);
}

.export-btn {
    background: var(--warning-gradient);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* Categories Card */
.categories-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.category-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.category-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.category-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.category-icon.food {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.category-icon.transport {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.category-icon.bills {
    background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
}

.category-icon.entertainment {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
}

.category-name {
    font-weight: 500;
    color: var(--text-primary);
}

.category-amount {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 80px;
    text-align: right;
}

.category-bar {
    width: 100px;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.category-progress {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Chart Card */
.chart-card {
    grid-column: span 2;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Transactions Card */
.transactions-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.transaction-icon.income {
    background: var(--success-gradient);
}

.transaction-icon.expense {
    background: var(--accent-gradient);
}

.transaction-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.transaction-name {
    font-weight: 500;
    color: var(--text-primary);
}

.transaction-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.transaction-amount {
    font-weight: 600;
    font-size: 1.125rem;
}

.transaction-amount.income {
    color: var(--income-color);
}

.transaction-amount.expense {
    color: var(--expense-color);
}

/* Insights Card */
.insights-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.insight-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.insight-icon.warning {
    background: var(--warning-gradient);
}

.insight-icon.success {
    background: var(--success-gradient);
}

.insight-icon.info {
    background: var(--secondary-gradient);
}

.insight-text {
    color: var(--text-secondary);
    line-height: 1.5;
}

.insight-text strong {
    color: var(--text-primary);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
}

.modal-content {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    margin: 5% auto;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--text-primary);
    font-weight: 600;
}

.close {
    color: var(--text-secondary);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--text-primary);
}

#transactionForm {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #11998e;
    box-shadow: 0 0 0 3px rgba(17, 153, 142, 0.2);
}

.submit-btn {
    width: 100%;
    padding: 1.25rem;
    background: var(--primary-gradient);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(17, 153, 142, 0.4);
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .balance-card,
    .chart-card {
        grid-column: span 1;
    }
    
    .balance-content {
        grid-template-columns: 1fr;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}