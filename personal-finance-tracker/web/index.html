<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Finance Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-chart-pie"></i>
                <h1>Finance Tracker</h1>
            </div>
            <div class="tagline">Track. Analyze. Prosper. 💰</div>
        </header>

        <div class="dashboard-grid">
            <!-- Balance Overview -->
            <div class="card balance-card">
                <div class="card-header">
                    <h3><i class="fas fa-wallet"></i> Balance Overview</h3>
                </div>
                <div class="balance-content">
                    <div class="balance-item">
                        <span class="balance-label">Total Balance</span>
                        <span class="balance-amount total">$12,845.67</span>
                    </div>
                    <div class="balance-item">
                        <span class="balance-label">Monthly Income</span>
                        <span class="balance-amount income">+$5,200.00</span>
                    </div>
                    <div class="balance-item">
                        <span class="balance-label">Monthly Expenses</span>
                        <span class="balance-amount expense">-$3,140.25</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card actions-card">
                <div class="card-header">
                    <h3><i class="fas fa-plus-circle"></i> Quick Actions</h3>
                </div>
                <div class="actions-grid">
                    <button class="action-btn income-btn" onclick="openTransactionModal('income')">
                        <i class="fas fa-arrow-up"></i>
                        Add Income
                    </button>
                    <button class="action-btn expense-btn" onclick="openTransactionModal('expense')">
                        <i class="fas fa-arrow-down"></i>
                        Add Expense
                    </button>
                    <button class="action-btn analyze-btn" onclick="analyzeFinances()">
                        <i class="fas fa-chart-line"></i>
                        Analyze
                    </button>
                    <button class="action-btn export-btn" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                </div>
            </div>

            <!-- Expense Categories -->
            <div class="card categories-card">
                <div class="card-header">
                    <h3><i class="fas fa-tags"></i> This Month's Categories</h3>
                </div>
                <div class="categories-list">
                    <div class="category-item">
                        <div class="category-info">
                            <i class="fas fa-utensils category-icon food"></i>
                            <span class="category-name">Food & Dining</span>
                        </div>
                        <div class="category-amount">$890.50</div>
                        <div class="category-bar">
                            <div class="category-progress" style="width: 75%"></div>
                        </div>
                    </div>
                    <div class="category-item">
                        <div class="category-info">
                            <i class="fas fa-car category-icon transport"></i>
                            <span class="category-name">Transportation</span>
                        </div>
                        <div class="category-amount">$245.30</div>
                        <div class="category-bar">
                            <div class="category-progress" style="width: 45%"></div>
                        </div>
                    </div>
                    <div class="category-item">
                        <div class="category-info">
                            <i class="fas fa-home category-icon bills"></i>
                            <span class="category-name">Bills & Utilities</span>
                        </div>
                        <div class="category-amount">$1,250.00</div>
                        <div class="category-bar">
                            <div class="category-progress" style="width: 90%"></div>
                        </div>
                    </div>
                    <div class="category-item">
                        <div class="category-info">
                            <i class="fas fa-gamepad category-icon entertainment"></i>
                            <span class="category-name">Entertainment</span>
                        </div>
                        <div class="category-amount">$185.75</div>
                        <div class="category-bar">
                            <div class="category-progress" style="width: 60%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Spending Chart -->
            <div class="card chart-card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-bar"></i> Spending Trends</h3>
                </div>
                <div class="chart-container">
                    <canvas id="spendingChart"></canvas>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="card transactions-card">
                <div class="card-header">
                    <h3><i class="fas fa-history"></i> Recent Transactions</h3>
                </div>
                <div class="transactions-list">
                    <div class="transaction-item">
                        <div class="transaction-icon expense">
                            <i class="fas fa-coffee"></i>
                        </div>
                        <div class="transaction-details">
                            <span class="transaction-name">Starbucks Coffee</span>
                            <span class="transaction-date">Today, 2:30 PM</span>
                        </div>
                        <div class="transaction-amount expense">-$4.95</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon income">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="transaction-details">
                            <span class="transaction-name">Salary Deposit</span>
                            <span class="transaction-date">Yesterday, 9:00 AM</span>
                        </div>
                        <div class="transaction-amount income">+$2,600.00</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon expense">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="transaction-details">
                            <span class="transaction-name">Shell Gas Station</span>
                            <span class="transaction-date">2 days ago, 6:45 PM</span>
                        </div>
                        <div class="transaction-amount expense">-$45.20</div>
                    </div>
                </div>
            </div>

            <!-- AI Insights -->
            <div class="card insights-card">
                <div class="card-header">
                    <h3><i class="fas fa-lightbulb"></i> AI Insights</h3>
                </div>
                <div class="insights-content">
                    <div class="insight-item">
                        <i class="fas fa-exclamation-triangle insight-icon warning"></i>
                        <div class="insight-text">
                            <strong>Budget Alert:</strong> You're 15% over your food budget this month.
                        </div>
                    </div>
                    <div class="insight-item">
                        <i class="fas fa-thumbs-up insight-icon success"></i>
                        <div class="insight-text">
                            <strong>Great Job:</strong> Transportation costs are 25% lower than last month!
                        </div>
                    </div>
                    <div class="insight-item">
                        <i class="fas fa-chart-line insight-icon info"></i>
                        <div class="insight-text">
                            <strong>Trend:</strong> Your savings rate improved by 8% compared to last quarter.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Modal -->
    <div id="transactionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add Transaction</h3>
                <span class="close" onclick="closeTransactionModal()">&times;</span>
            </div>
            <form id="transactionForm">
                <div class="form-group">
                    <label for="amount">Amount</label>
                    <input type="number" id="amount" step="0.01" placeholder="0.00" required>
                </div>
                <div class="form-group">
                    <label for="description">Description</label>
                    <input type="text" id="description" placeholder="Enter description" required>
                </div>
                <div class="form-group">
                    <label for="category">Category</label>
                    <select id="category" required>
                        <option value="">Select category</option>
                        <option value="food">Food & Dining</option>
                        <option value="transport">Transportation</option>
                        <option value="bills">Bills & Utilities</option>
                        <option value="entertainment">Entertainment</option>
                        <option value="shopping">Shopping</option>
                        <option value="health">Healthcare</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="date">Date</label>
                    <input type="date" id="date" required>
                </div>
                <button type="submit" class="submit-btn">Add Transaction</button>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>