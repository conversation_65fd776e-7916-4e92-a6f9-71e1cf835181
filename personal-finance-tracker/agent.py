import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../shared'))

import logging
import asyncio
from src.models import AgentState, AgentStatus
from tools.transaction_categorizer_tool import transaction_categorizer_tool
from tools.budget_analyzer_tool import budget_analyzer_tool

class AgentRunner:
    def __init__(self):
        self.state = AgentState()
        logging.basicConfig(level=logging.INFO)
        
    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")

        try:
            # Step 1: Categorize transactions
            transactions = self.state.research_data.get("transactions", [])
            categorized = []
            for transaction in transactions:
                result = await transaction_categorizer_tool.execute(state=self.state, transaction=transaction)
                if not result.success:
                    raise Exception(f"Categorization failed: {result.error}")
                categorized.append(result.data)
            self.state.research_data["categorized"] = categorized
            logging.info("Step 1: Transactions categorized.")

            # Step 2: Analyze budget
            budget = {"Food": 500, "Transport": 200, "Entertainment": 100, "Bills": 1000, "Other": 300}  # Example budget
            analysis_result = await budget_analyzer_tool.execute(state=self.state, expenses=categorized, budget=budget)
            if not analysis_result.success:
                raise Exception(f"Budget analysis failed: {analysis_result.error}")
            self.state.final_output = analysis_result.data
            logging.info("Step 2: Budget analyzed.")

            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")

        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")

        finally:
            self.present_output()
            
    def present_output(self):
        if self.state.status == AgentStatus.COMPLETED:
            print(f"Budget Analysis Results:")
            print(f"Category Totals: {self.state.final_output['totals']}")
            print(f"Budget Alerts: {self.state.final_output['alerts']}")
        else:
            print(f"Agent failed with errors: {self.state.error_log}")

if __name__ == "__main__":
    agent = AgentRunner()
    agent.state.topic = "monthly_budget_analysis"
    # Example transactions
    agent.state.research_data["transactions"] = [
        {"description": "McDonald's", "amount": 15.50},
        {"description": "Uber ride", "amount": 23.00},
        {"description": "Netflix subscription", "amount": 12.99}
    ]
    asyncio.run(agent.run_pipeline())