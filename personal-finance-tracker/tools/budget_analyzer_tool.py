import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../shared'))

from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings

class BudgetAnalyzerTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="budget_analyzer",
            description="Analyzes expenses against budget and suggests adjustments."
        )

    async def execute(self, state: AgentState, expenses: list, budget: dict) -> ToolResult:
        self.logger.info("Analyzing budget.")
        try:
            category_totals = {}
            for expense in expenses:
                category = expense["category"]
                amount = expense["transaction"]["amount"]
                category_totals[category] = category_totals.get(category, 0) + amount

            alerts = []
            for category, total in category_totals.items():
                if total > budget.get(category, 0):
                    alerts.append(f"Overspent in {category}: ${total - budget[category]:.2f}")

            return ToolResult.success_result({"totals": category_totals, "alerts": alerts})
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

budget_analyzer_tool = BudgetAnalyzerTool()