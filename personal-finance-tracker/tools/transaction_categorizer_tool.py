import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../shared'))

from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class TransactionCategorizerTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="transaction_categorizer",
            description="Categorizes bank transactions using OpenAI."
        )

    async def execute(self, state: AgentState, transaction: dict) -> ToolResult:
        self.logger.info(f"Categorizing transaction: {transaction['description']}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.OPENAI_API_KEY}"}
                payload = {
                    "model": "gpt-4o-mini",
                    "messages": [
                        {"role": "system", "content": "Categorize this transaction into one of: Food, Transport, Entertainment, Bills, Other."},
                        {"role": "user", "content": f"Description: {transaction['description']}, Amount: {transaction['amount']}"}
                    ]
                }
                async with session.post("https://api.openai.com/v1/chat/completions", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        category = data["choices"][0]["message"]["content"]
                        return ToolResult.success_result({"category": category, "transaction": transaction})
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

transaction_categorizer_tool = TransactionCategorizerTool()