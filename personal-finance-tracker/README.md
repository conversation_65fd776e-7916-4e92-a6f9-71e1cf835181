# 💰 Personal Finance Tracker

> **AI-Powered Financial Intelligence for Smart Money Management**

Track. Analyze. Prosper. Transform your financial chaos into crystal-clear insights with intelligent categorization, budgeting, and automated financial analysis.

![Finance Tracker](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)
![Python](https://img.shields.io/badge/Python-3.8+-blue)
![Security](https://img.shields.io/badge/Security-Bank%20Grade-gold)
![License](https://img.shields.io/badge/License-MIT-green)

## ✨ Features

### 🧠 **AI-Powered Categorization**
- **Smart Classification**: Automatically categorize transactions using OpenAI
- **Learning Algorithm**: Improves accuracy over time with your spending patterns
- **Custom Categories**: Food, Transport, Entertainment, Bills, Healthcare, and more
- **Merchant Recognition**: Identify and remember recurring merchants

### 📊 **Advanced Analytics**
- **Budget Tracking**: Real-time budget monitoring with alerts
- **Spending Trends**: Visual charts showing your financial patterns
- **Expense Forecasting**: Predict future spending based on historical data
- **Category Insights**: Deep dive into spending by category

### 🔔 **Intelligent Alerts**
- **Budget Warnings**: Get notified before overspending
- **Unusual Activity**: Detect abnormal spending patterns
- **Bill Reminders**: Never miss a payment deadline
- **Savings Goals**: Track progress toward financial objectives

### 🏦 **Bank Integration**
- **Plaid API Support**: Connect to 10,000+ financial institutions
- **Real-time Sync**: Automatic transaction import
- **Multiple Accounts**: Checking, savings, credit cards, investments
- **Security First**: Bank-grade encryption and security protocols

## 🎯 Use Cases

- **Personal Finance**: Individuals managing personal budgets and expenses
- **Family Budgeting**: Households tracking shared expenses and goals
- **Freelancers**: Self-employed professionals managing irregular income
- **Small Business**: Entrepreneurs separating personal and business finances

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- OpenAI API Key
- Optional: Plaid API credentials for bank integration

### Installation

1. **Clone and Navigate**
   ```bash
   git clone <repository-url>
   cd personal-finance-tracker
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

4. **Run the Application**
   ```bash
   # Command Line
   python agent.py
   
   # Web Interface
   python -m http.server 8000 --directory web
   # Open http://localhost:8000
   ```

## 🌐 Web Dashboard

Experience a beautiful, intuitive financial dashboard designed for clarity and insight:

### 🎨 **Design Features**
- **Modern Interface**: Clean, professional design with green/teal color scheme
- **Interactive Charts**: Real-time spending trends and budget visualization  
- **Mobile-First**: Responsive design that works on all devices
- **Glassmorphism**: Modern blurred glass effects for visual appeal

### 📱 **Dashboard Components**
- **Balance Overview**: Total balance, income, and expenses at a glance
- **Quick Actions**: Add income/expenses with one click
- **Category Breakdown**: Visual spending by category with progress bars
- **Recent Transactions**: Latest financial activity with smart icons
- **AI Insights**: Personalized recommendations and alerts

### 📊 **Interactive Analytics**
- **Spending Trends Chart**: Monthly income vs expenses visualization
- **Category Distribution**: Pie charts and progress indicators
- **Budget Monitoring**: Real-time budget adherence tracking
- **Goal Progress**: Visual tracking of savings and financial goals

## ⚙️ Configuration

### Environment Variables
```env
OPENAI_API_KEY=your_openai_api_key
AGENT_MODEL=gpt-4o-mini
LOG_LEVEL=INFO
PLAID_CLIENT_ID=your_plaid_client_id
PLAID_SECRET=your_plaid_secret
```

### Budget Customization
```python
# Set your monthly budget limits
BUDGET_LIMITS = {
    "Food": 500,
    "Transport": 200,
    "Entertainment": 150,
    "Bills": 1000,
    "Healthcare": 300,
    "Shopping": 400,
    "Other": 200
}
```

## 🔌 API Integration

### Add Transaction
```python
POST /add-transaction
{
    "amount": 25.50,
    "description": "Coffee shop purchase",
    "category": "food",
    "type": "expense",
    "date": "2024-01-15"
}
```

### Get Analytics
```python
GET /analytics?period=monthly
{
    "total_income": 5200.00,
    "total_expenses": 3140.25,
    "categories": {...},
    "trends": {...}
}
```

## 🧠 AI-Powered Features

### Smart Categorization
- **Context Understanding**: Analyzes transaction descriptions for accurate categorization
- **Merchant Learning**: Remembers merchant-category associations
- **Pattern Recognition**: Identifies recurring transactions and subscriptions
- **Anomaly Detection**: Flags unusual or suspicious transactions

### Financial Insights
```
✅ Great Job: Transportation costs are 25% lower than last month!
⚠️ Budget Alert: You're 15% over your food budget this month.
📈 Trend: Your savings rate improved by 8% compared to last quarter.
```

## 📊 Advanced Analytics

### Spending Analysis
- **Monthly Trends**: Track spending patterns over time
- **Category Breakdown**: Detailed analysis by expense category
- **Budget Performance**: Compare actual vs budgeted amounts
- **Savings Rate**: Calculate and track your savings percentage

### Financial Health Score
- **Comprehensive Scoring**: Overall financial health assessment
- **Improvement Suggestions**: AI-powered recommendations
- **Goal Tracking**: Progress toward financial objectives
- **Comparative Analysis**: Benchmark against similar demographics

## 🔒 Security & Privacy

### Data Protection
- **End-to-End Encryption**: All financial data encrypted in transit and at rest
- **Local Processing**: Sensitive calculations performed locally
- **No Data Retention**: Transaction details not stored on external servers
- **GDPR Compliance**: Full privacy regulation compliance

### Security Features
- **Multi-Factor Authentication**: Optional 2FA for account access
- **Session Management**: Automatic logout and session timeout
- **Audit Logging**: Complete audit trail of all financial activities
- **Bank-Grade Security**: Same security standards as major banks

## 🤝 n8n Workflow Integration

### Automated Financial Workflow
1. **Bank Sync**: Daily transaction import via Plaid API
2. **AI Categorization**: Automatic expense classification
3. **Budget Analysis**: Real-time budget monitoring
4. **Alert Generation**: Smart notifications for overspending
5. **Report Creation**: Weekly/monthly financial summaries
6. **Goal Tracking**: Progress updates on savings goals

### Integration Options
- **Webhook Triggers**: React to new transactions in real-time
- **Scheduled Reports**: Automated daily/weekly/monthly summaries
- **Third-party Sync**: Integration with accounting software
- **Mobile Notifications**: Push notifications for important alerts

## 📱 Mobile Experience

### Responsive Design
- **Touch-Optimized**: Perfect for mobile transaction entry
- **Offline Mode**: Work without internet connection
- **Fast Loading**: Optimized for mobile networks
- **Gesture Support**: Swipe and tap interactions

### Mobile Features
- **Quick Entry**: Add transactions with minimal taps
- **Photo Receipts**: Capture and categorize receipts
- **Voice Input**: Speak transaction details
- **Location Tracking**: Auto-detect merchant locations

## 📈 Reporting & Export

### Report Types
- **Monthly Statements**: Comprehensive monthly financial reports
- **Category Reports**: Detailed spending by category
- **Budget Reports**: Budget vs actual performance analysis
- **Tax Reports**: Year-end summaries for tax preparation

### Export Formats
```python
# Available export formats
EXPORT_FORMATS = [
    'CSV',      # Spreadsheet compatibility
    'PDF',      # Professional reports
    'JSON',     # API integration
    'Excel',    # Advanced analysis
    'QIF/OFX'   # Accounting software import
]
```

## 🎨 Theme Customization

The web interface features a sophisticated financial design:
- **Color Palette**: Professional greens and teals conveying prosperity
- **Typography**: Clean, readable fonts for financial data clarity
- **Charts**: Interactive Chart.js visualizations
- **Icons**: Financial-specific iconography for intuitive navigation

## 💡 Smart Features

### Automated Insights
- **Spending Patterns**: Identify where your money goes
- **Budget Optimization**: Suggestions for better budget allocation
- **Savings Opportunities**: Find areas to reduce expenses
- **Investment Readiness**: Assess when you're ready to invest

### Future Enhancements
- **Investment Tracking**: Portfolio management and analysis
- **Credit Score Monitoring**: Track and improve your credit score
- **Bill Prediction**: Forecast upcoming bills and payments
- **Financial Planning**: Long-term financial goal planning

## 🔧 Troubleshooting

### Common Issues
- **Bank Connection**: Verify Plaid API credentials and bank support
- **Categorization Accuracy**: Retrain AI with manual corrections
- **Performance**: Check database size and consider archiving old data

### Support Resources
- Detailed logging in `logs/finance-tracker.log`
- Configuration validation tools
- Sample data for testing

## 📄 License

MIT License - see LICENSE file for details

---

**Take control of your financial future today!** Start tracking, analyzing, and optimizing your money management with AI-powered insights. 💰📊✨