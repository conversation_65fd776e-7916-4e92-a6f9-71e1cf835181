Based on the Reddit post about n8n's hybrid approach and the Modular Python Agent Framework documentation, I'll develop three complex hybrid applications that combine n8n's orchestration capabilities with the Python agent's modular architecture. Each application will include n8n workflow details and Python code for custom tools, ensuring scalability and production-readiness. The applications are designed to leverage n8n for coordination and external services (via Python tools) for heavy lifting, aligning with the hybrid philosophy.

---

### Application 1: Social Media Content Automator

**Description**: This application automates the creation, optimization, and scheduling of social media content across platforms like X, LinkedIn, and Instagram. n8n orchestrates the workflow, while Python tools handle content generation, image creation, and analytics processing.

**Use Case**: Ideal for marketers or small businesses looking to maintain a consistent social media presence without manual effort.

**Architecture**:
- **n8n Workflow**: Monitors Google Trends for trending topics, triggers Python tools for content generation, and schedules posts via Buffer API.
- **Python Tools**: 
  - `ContentGeneratorTool`: Uses Gemini API to generate platform-specific posts.
  - `ImageCreatorTool`: Uses Replicate API to create visuals.
  - `AnalyticsProcessorTool`: Analyzes engagement data from Buffer.

**n8n Workflow Setup**:
1. **Webhook Node**: Triggers on a cron schedule (e.g., daily at 8 AM).
2. **HTTP Request Node**: Fetches trending topics from Google Trends API.
3. **Function Node**: Filters topics relevant to the user’s niche.
4. **HTTP Request Node**: Calls the Python agent’s endpoint (deployed as a serverless function) to generate content and images.
5. **Buffer Node**: Schedules posts across X, LinkedIn, and Instagram.
6. **HTTP Request Node**: Calls the Python agent to process analytics data.
7. **Google Sheets Node**: Logs performance metrics.

**Python Code**:

**`tools/content_generator_tool.py`**:
```python
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class ContentGeneratorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="content_generator",
            description="Generates platform-specific social media posts using Gemini API."
        )

    async def execute(self, state: AgentState, platform: str, topic: str) -> ToolResult:
        self.logger.info(f"Generating content for {platform} on topic: {topic}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.GEMINI_API_KEY}"}
                payload = {
                    "model": settings.AGENT_MODEL,
                    "prompt": f"Generate a {platform} post about {topic}. Keep it engaging and under 280 characters."
                }
                async with session.post("https://api.google.com/gemini/v1/generate", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result(data["text"])
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

content_generator_tool = ContentGeneratorTool()
```

**`tools/image_creator_tool.py`**:
```python
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class ImageCreatorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="image_creator",
            description="Creates visuals for social media posts using Replicate API."
        )

    async def execute(self, state: AgentState, topic: str) -> ToolResult:
        self.logger.info(f"Creating image for topic: {topic}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Token {settings.REPLICATE_API_TOKEN}"}
                payload = {
                    "prompt": f"A vibrant illustration of {topic} for social media",
                    "model": "stable-diffusion"
                }
                async with session.post("https://api.replicate.com/v1/predictions", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result(data["output_url"])
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

image_creator_tool = ImageCreatorTool()
```

**`agent.py` (Updated Pipeline)**:
```python
# ... (previous imports and class definition remain)
from tools.content_generator_tool import content_generator_tool
from tools.image_creator_tool import image_creator_tool

class AgentRunner:
    # ... (previous __init__ and other methods remain)
    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")

        try:
            # Step 1: Generate content for X
            content_result = await content_generator_tool.execute(state=self.state, platform="X", topic=self.state.topic)
            if not content_result.success:
                raise Exception(f"Content generation failed: {content_result.error}")
            self.state.research_data["x_content"] = content_result.data
            logging.info("Step 1: X content generated.")

            # Step 2: Create image
            image_result = await image_creator_tool.execute(state=self.state, topic=self.state.topic)
            if not image_result.success:
                raise Exception(f"Image creation failed: {image_result.error}")
            self.state.research_data["image_url"] = image_result.data
            logging.info("Step 2: Image created.")

            self.state.final_output = {"content": self.state.research_data["x_content"], "image": self.state.research_data["image_url"]}
            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")

        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")

        finally:
            self.present_output()
# ... (rest of the file remains)
```

**Deployment**:
- Deploy the Python agent as a serverless function (e.g., AWS Lambda) with an HTTP endpoint.
- Host n8n on a low-cost server or an Android phone using Termux for testing.
- Configure Buffer and API keys in `.env`.

---

### Application 2: Personal Finance Tracker

**Description**: This application automates expense tracking, categorization, and budgeting by integrating bank APIs with Google Sheets. n8n handles orchestration, while Python tools process transactions and generate insights.

**Use Case**: Perfect for individuals or freelancers managing personal or small business finances.

**Architecture**:
- **n8n Workflow**: Monitors bank transactions via Plaid API, triggers Python tools for categorization, and updates Google Sheets.
- **Python Tools**:
  - `TransactionCategorizerTool`: Uses OpenAI to categorize expenses.
  - `BudgetAnalyzerTool`: Calculates budget adherence and suggests adjustments.

**n8n Workflow Setup**:
1. **Webhook Node**: Triggers on new transactions via Plaid webhook.
2. **HTTP Request Node**: Fetches transaction details from Plaid API.
3. **HTTP Request Node**: Calls the Python agent to categorize transactions.
4. **Google Sheets Node**: Updates expense records.
5. **HTTP Request Node**: Calls the Python agent to analyze budget.
6. **Email Node**: Sends budget alerts if overspending detected.

**Python Code**:

**`tools/transaction_categorizer_tool.py`**:
```python
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class TransactionCategorizerTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="transaction_categorizer",
            description="Categorizes bank transactions using OpenAI."
        )

    async def execute(self, state: AgentState, transaction: dict) -> ToolResult:
        self.logger.info(f"Categorizing transaction: {transaction['description']}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.OPENAI_API_KEY}"}
                payload = {
                    "model": "gpt-4o-mini",
                    "messages": [
                        {"role": "system", "content": "Categorize this transaction into one of: Food, Transport, Entertainment, Bills, Other."},
                        {"role": "user", "content": f"Description: {transaction['description']}, Amount: {transaction['amount']}"}
                    ]
                }
                async with session.post("https://api.openai.com/v1/chat/completions", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        category = data["choices"][0]["message"]["content"]
                        return ToolResult.success_result({"category": category, "transaction": transaction})
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

transaction_categorizer_tool = TransactionCategorizerTool()
```

**`tools/budget_analyzer_tool.py`**:
```python
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings

class BudgetAnalyzerTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="budget_analyzer",
            description="Analyzes expenses against budget and suggests adjustments."
        )

    async def execute(self, state: AgentState, expenses: list, budget: dict) -> ToolResult:
        self.logger.info("Analyzing budget.")
        try:
            category_totals = {}
            for expense in expenses:
                category = expense["category"]
                amount = expense["transaction"]["amount"]
                category_totals[category] = category_totals.get(category, 0) + amount

            alerts = []
            for category, total in category_totals.items():
                if total > budget.get(category, 0):
                    alerts.append(f"Overspent in {category}: ${total - budget[category]:.2f}")

            return ToolResult.success_result({"totals": category_totals, "alerts": alerts})
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

budget_analyzer_tool = BudgetAnalyzerTool()
```

**`agent.py` (Updated Pipeline)**:
```python
# ... (previous imports and class definition remain)
from tools.transaction_categorizer_tool import transaction_categorizer_tool
from tools.budget_analyzer_tool import budget_analyzer_tool

class AgentRunner:
    # ... (previous __init__ and other methods remain)
    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")

        try:
            # Step 1: Categorize transactions
            transactions = self.state.research_data.get("transactions", [])
            categorized = []
            for transaction in transactions:
                result = await transaction_categorizer_tool.execute(state=self.state, transaction=transaction)
                if not result.success:
                    raise Exception(f"Categorization failed: {result.error}")
                categorized.append(result.data)
            self.state.research_data["categorized"] = categorized
            logging.info("Step 1: Transactions categorized.")

            # Step 2: Analyze budget
            budget = {"Food": 500, "Transport": 200, "Entertainment": 100, "Bills": 1000, "Other": 300}  # Example budget
            analysis_result = await budget_analyzer_tool.execute(state=self.state, expenses=categorized, budget=budget)
            if not analysis_result.success:
                raise Exception(f"Budget analysis failed: {analysis_result.error}")
            self.state.final_output = analysis_result.data
            logging.info("Step 2: Budget analyzed.")

            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")

        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")

        finally:
            self.present_output()
# ... (rest of the file remains)
```

**Deployment**:
- Deploy the Python agent on a cloud provider like Render.
- Host n8n on DigitalOcean for reliability.
- Configure Plaid and OpenAI API keys in `.env`.

---

### Application 3: Startup Idea Generator

**Description**: This application scrapes Reddit for unmet needs, analyzes trends, and generates startup ideas using AI. n8n orchestrates the process, while Python tools handle scraping and idea generation.

**Use Case**: Useful for entrepreneurs or innovation teams seeking data-driven business opportunities.

**Architecture**:
- **n8n Workflow**: Monitors Reddit via API, triggers Python tools for scraping and analysis, and stores ideas in Notion.
- **Python Tools**:
  - `RedditScraperTool`: Scrapes posts from relevant subreddits.
  - `IdeaGeneratorTool`: Uses Gemini to generate startup ideas based on scraped data.

**n8n Workflow Setup**:
1. **Cron Node**: Runs weekly to check for new Reddit posts.
2. **HTTP Request Node**: Fetches posts from subreddits like r/Entrepreneur.
3. **HTTP Request Node**: Calls the Python agent to scrape and process posts.
4. **HTTP Request Node**: Calls the Python agent to generate ideas.
5. **Notion Node**: Stores ideas in a database.
6. **Email Node**: Notifies the user of new ideas.

**Python Code**:

**`tools/reddit_scraper_tool.py`**:
```python
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
import aiohttp

class RedditScraperTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="reddit_scraper",
            description="Scrapes posts from specified Reddit subreddits."
        )

    async def execute(self, state: AgentState, subreddit: str, limit: int = 10) -> ToolResult:
        self.logger.info(f"Scraping r/{subreddit}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"User-Agent": "n8n-agent/1.0"}
                url = f"https://www.reddit.com/r/{subreddit}/new.json?limit={limit}"
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        posts = [post["data"]["title"] for post in data["data"]["children"]]
                        return ToolResult.success_result(posts)
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

reddit_scraper_tool = RedditScraperTool()
```

**`tools/idea_generator_tool.py`**:
```python
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class IdeaGeneratorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="idea_generator",
            description="Generates startup ideas based on Reddit posts using Gemini."
        )

    async def execute(self, state: AgentState, posts: list) -> ToolResult:
        self.logger.info("Generating startup ideas.")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.GEMINI_API_KEY}"}
                prompt = f"Based on these Reddit posts: {', '.join(posts[:5])}, suggest 3 startup ideas addressing unmet needs."
                payload = {
                    "model": settings.AGENT_MODEL,
                    "prompt": prompt
                }
                async with session.post("https://api.google.com/gemini/v1/generate", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result(data["text"])
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

idea_generator_tool = IdeaGeneratorTool()
```

**`agent.py` (Updated Pipeline)**:
```python
# ... (previous imports and class definition remain)
from tools.reddit_scraper_tool import reddit_scraper_tool
from tools.idea_generator_tool import idea_generator_tool

class AgentRunner:
    # ... (previous __init__ and other methods remain)
    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")

        try:
            # Step 1: Scrape Reddit
            scrape_result = await reddit_scraper_tool.execute(state=self.state, subreddit=self.state.topic, limit=10)
            if not scrape_result.success:
                raise Exception(f"Scraping failed: {scrape_result.error}")
            self.state.research_data["posts"] = scrape_result.data
            logging.info("Step 1: Reddit posts scraped.")

            # Step 2: Generate ideas
            idea_result = await idea_generator_tool.execute(state=self.state, posts=self.state.research_data["posts"])
            if not idea_result.success:
                raise Exception(f"Idea generation failed: {idea_result.error}")
            self.state.final_output = idea_result.data
            logging.info("Step 2: Startup ideas generated.")

            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")

        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")

        finally:
            self.present_output()
# ... (rest of the file remains)
```

**Deployment**:
- Deploy the Python agent on Google Cloud Functions.
- Self-host n8n on an Android phone for cost savings.
- Configure Reddit and Gemini API keys in `.env`.

---

### Notes:
- **Scalability**: Each application uses n8n for lightweight orchestration and Python for heavy processing, ensuring performance at scale.
- **Modularity**: The Python tools follow the Modular Agent Framework, making them reusable across workflows.
- **Cost-Effectiveness**: Self-hosting options (e.g., Android phone) and free cloud tiers minimize expenses.
- **Extensibility**: New tools can be added by copying `tool_template.py` and integrating with n8n nodes.

These applications demonstrate the power of the hybrid approach, combining n8n’s orchestration with Python’s computational capabilities to create robust, user-friendly automation solutions. For further customization, explore n8n’s template library or extend the Python framework with additional tools.