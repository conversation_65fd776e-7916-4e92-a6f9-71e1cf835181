# n8n-MPAF Applications

This repository contains three hybrid applications that combine n8n's orchestration capabilities with the Modular Python Agent Framework (MPAF). Each application is organized in its own subdirectory with dedicated tools and agent pipelines.

## Applications

### 1. Social Media Content Automator (`social-media-automator/`)
Automates creation, optimization, and scheduling of social media content across platforms.

**Features:**
- Content generation using Gemini API
- Image creation using Replicate API
- Platform-specific post optimization

**Setup:**
```bash
cd social-media-automator
cp .env.example .env
# Add your API keys to .env
pip install -r requirements.txt
python agent.py
```

### 2. Personal Finance Tracker (`personal-finance-tracker/`)
Automates expense tracking, categorization, and budgeting with AI-powered insights.

**Features:**
- Transaction categorization using OpenAI
- Budget analysis and alerts
- Expense tracking automation

**Setup:**
```bash
cd personal-finance-tracker
cp .env.example .env
# Add your API keys to .env
pip install -r requirements.txt
python agent.py
```

### 3. Startup Idea Generator (`startup-idea-generator/`)
Scrapes Reddit for unmet needs and generates data-driven startup ideas.

**Features:**
- Reddit post scraping
- AI-powered idea generation using Gemini
- Trend analysis and opportunity identification

**Setup:**
```bash
cd startup-idea-generator
cp .env.example .env
# Add your API keys to .env
pip install -r requirements.txt
python agent.py
```

## Shared Framework (`shared/`)
Contains common base classes and utilities used by all applications:
- `tools/base_tool.py` - Base tool interface
- `src/models.py` - Data models and types
- `config/settings.py` - Configuration management

## Architecture
Each application follows the hybrid n8n-MPAF pattern:
- **n8n**: Handles orchestration, external service integration, and workflow management
- **Python Tools**: Process data, interact with APIs, and perform complex computations
- **Modular Design**: Tools can be reused across different workflows and applications

## Getting Started
1. Choose an application directory
2. Copy the `.env.example` to `.env` and configure your API keys
3. Install dependencies: `pip install -r requirements.txt`
4. Run the agent: `python agent.py`

For n8n integration, deploy the Python agents as serverless functions and call them from n8n HTTP Request nodes.