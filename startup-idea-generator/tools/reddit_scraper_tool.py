import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../shared'))

from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
import aiohttp

class RedditScraperTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="reddit_scraper",
            description="Scrapes posts from specified Reddit subreddits."
        )

    async def execute(self, state: AgentState, subreddit: str, limit: int = 10) -> ToolResult:
        self.logger.info(f"Scraping r/{subreddit}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"User-Agent": "n8n-agent/1.0"}
                url = f"https://www.reddit.com/r/{subreddit}/new.json?limit={limit}"
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        posts = [post["data"]["title"] for post in data["data"]["children"]]
                        return ToolResult.success_result(posts)
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

reddit_scraper_tool = RedditScraperTool()