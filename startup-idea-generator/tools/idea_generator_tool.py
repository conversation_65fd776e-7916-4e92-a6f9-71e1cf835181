import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../shared'))

from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class IdeaGeneratorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="idea_generator",
            description="Generates startup ideas based on Reddit posts using Gemini."
        )

    async def execute(self, state: AgentState, posts: list) -> ToolResult:
        self.logger.info("Generating startup ideas.")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.GEMINI_API_KEY}"}
                prompt = f"Based on these Reddit posts: {', '.join(posts[:5])}, suggest 3 startup ideas addressing unmet needs."
                payload = {
                    "model": settings.AGENT_MODEL,
                    "prompt": prompt
                }
                async with session.post("https://api.google.com/gemini/v1/generate", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result(data["text"])
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

idea_generator_tool = IdeaGeneratorTool()