import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../shared'))

import logging
import asyncio
from src.models import AgentState, AgentStatus
from tools.reddit_scraper_tool import reddit_scraper_tool
from tools.idea_generator_tool import idea_generator_tool

class AgentRunner:
    def __init__(self):
        self.state = AgentState()
        logging.basicConfig(level=logging.INFO)
        
    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")

        try:
            # Step 1: Scrape Reddit
            scrape_result = await reddit_scraper_tool.execute(state=self.state, subreddit=self.state.topic, limit=10)
            if not scrape_result.success:
                raise Exception(f"Scraping failed: {scrape_result.error}")
            self.state.research_data["posts"] = scrape_result.data
            logging.info("Step 1: Reddit posts scraped.")

            # Step 2: Generate ideas
            idea_result = await idea_generator_tool.execute(state=self.state, posts=self.state.research_data["posts"])
            if not idea_result.success:
                raise Exception(f"Idea generation failed: {idea_result.error}")
            self.state.final_output = idea_result.data
            logging.info("Step 2: Startup ideas generated.")

            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")

        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")

        finally:
            self.present_output()
            
    def present_output(self):
        if self.state.status == AgentStatus.COMPLETED:
            print(f"Startup Ideas Generated:")
            print(self.state.final_output)
        else:
            print(f"Agent failed with errors: {self.state.error_log}")

if __name__ == "__main__":
    agent = AgentRunner()
    agent.state.topic = "entrepreneur"
    asyncio.run(agent.run_pipeline())