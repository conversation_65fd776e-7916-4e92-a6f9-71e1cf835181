// Startup Idea Generator JavaScript
let isGenerating = false;
let currentIdeas = [];

// Mock data for demonstration
const mockIdeas = [
    {
        id: 1,
        title: "TaskMate - AI Personal Assistant",
        description: "An AI-powered personal assistant that learns from Reddit discussions about daily struggles and automates routine tasks. Integrates with smart home devices and calendar apps.",
        score: 95,
        tags: ["AI", "Productivity", "Mobile App"],
        market: "Personal Productivity",
        difficulty: "Medium",
        investment: "$50K - $100K"
    },
    {
        id: 2,
        title: "LocalFix - Neighborhood Service Marketplace",
        description: "A hyperlocal platform connecting people with skilled neighbors for quick fixes and services. Based on frequent Reddit posts about finding reliable local help.",
        score: 88,
        tags: ["Marketplace", "Local Services", "Community"],
        market: "Service Economy",
        difficulty: "High",
        investment: "$100K - $500K"
    },
    {
        id: 3,
        title: "MoodSpace - Mental Health Check-ins",
        description: "A gentle mental health app that uses Reddit sentiment analysis to provide personalized check-ins and resources. Privacy-focused with community support.",
        score: 92,
        tags: ["HealthTech", "Mental Health", "Community"],
        market: "Digital Health",
        difficulty: "Medium",
        investment: "$25K - $75K"
    }
];

const mockTrendingTopics = [
    "Remote work productivity tools",
    "Sustainable living solutions",
    "AI-powered automation",
    "Mental health support platforms",
    "Local community connections"
];

const mockPainPoints = [
    "Difficulty finding reliable local services",
    "Information overload in decision making",
    "Lack of personalized productivity solutions",
    "Limited mental health resources",
    "Complex setup for smart home devices"
];

const mockOpportunities = [
    "Growing remote work market",
    "Increased focus on mental wellness",
    "Rising demand for local services",
    "AI integration in daily workflows",
    "Community-driven solutions"
];

function generateIdeas() {
    if (isGenerating) return;
    
    const subreddit = document.getElementById('subreddit').value;
    const industry = document.getElementById('industry').value;
    const ideaCount = parseInt(document.getElementById('ideaCount').value);
    
    isGenerating = true;
    const generateBtn = document.querySelector('.generate-btn');
    const originalText = generateBtn.innerHTML;
    
    // Update button to show loading state
    generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing Reddit...';
    generateBtn.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';
    
    // Show progress stages
    const stages = [
        'Scraping r/' + subreddit + '...',
        'Analyzing trending topics...',
        'Identifying pain points...',
        'Generating AI ideas...',
        'Validating opportunities...'
    ];
    
    let currentStage = 0;
    const stageInterval = setInterval(() => {
        if (currentStage < stages.length) {
            generateBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${stages[currentStage]}`;
            currentStage++;
        }
    }, 1000);
    
    // Simulate API call
    setTimeout(() => {
        clearInterval(stageInterval);
        
        // Generate ideas based on count
        currentIdeas = mockIdeas.slice(0, ideaCount);
        
        // Display results
        displayResults();
        
        // Reset button
        generateBtn.innerHTML = originalText;
        generateBtn.style.background = 'var(--primary-gradient)';
        isGenerating = false;
        
        // Update stats
        updateStats();
        
        showNotification('Successfully generated ' + ideaCount + ' startup ideas!', 'success');
    }, 5000);
}

function displayResults() {
    const resultsSection = document.getElementById('results');
    const ideasGrid = document.getElementById('ideasGrid');
    
    // Clear previous results
    ideasGrid.innerHTML = '';
    
    // Generate idea cards
    currentIdeas.forEach(idea => {
        const ideaCard = createIdeaCard(idea);
        ideasGrid.appendChild(ideaCard);
    });
    
    // Populate analysis sections
    populateAnalysis();
    
    // Show results
    resultsSection.style.display = 'block';
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

function createIdeaCard(idea) {
    const card = document.createElement('div');
    card.className = 'idea-card';
    card.onclick = () => openIdeaModal(idea);
    
    card.innerHTML = `
        <div class="idea-header">
            <div class="idea-title">${idea.title}</div>
            <div class="idea-score">${idea.score}%</div>
        </div>
        <div class="idea-description">${idea.description}</div>
        <div class="idea-tags">
            ${idea.tags.map(tag => `<span class="idea-tag">${tag}</span>`).join('')}
        </div>
        <div class="idea-metrics">
            <span><i class="fas fa-chart-line"></i> ${idea.market}</span>
            <span><i class="fas fa-star"></i> ${idea.difficulty}</span>
        </div>
    `;
    
    return card;
}

function populateAnalysis() {
    // Populate trending topics
    const trendingList = document.getElementById('trendingList');
    trendingList.innerHTML = '';
    mockTrendingTopics.forEach(topic => {
        const item = document.createElement('div');
        item.className = 'trending-item';
        item.textContent = topic;
        trendingList.appendChild(item);
    });
    
    // Populate pain points
    const painPointsList = document.getElementById('painPointsList');
    painPointsList.innerHTML = '';
    mockPainPoints.forEach(point => {
        const item = document.createElement('div');
        item.className = 'pain-point-item';
        item.textContent = point;
        painPointsList.appendChild(item);
    });
    
    // Populate opportunities
    const opportunitiesList = document.getElementById('opportunitiesList');
    opportunitiesList.innerHTML = '';
    mockOpportunities.forEach(opportunity => {
        const item = document.createElement('div');
        item.className = 'opportunity-item';
        item.textContent = opportunity;
        opportunitiesList.appendChild(item);
    });
}

function openIdeaModal(idea) {
    const modal = document.getElementById('ideaModal');
    const title = document.getElementById('ideaTitle');
    const details = document.getElementById('ideaDetails');
    
    title.textContent = idea.title;
    details.innerHTML = `
        <div style="margin-bottom: 2rem;">
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">Description</h4>
            <p>${idea.description}</p>
        </div>
        
        <div style="margin-bottom: 2rem;">
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">Market Analysis</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div style="background: rgba(255,255,255,0.05); padding: 1rem; border-radius: 10px;">
                    <strong style="color: var(--text-primary);">Market:</strong><br>
                    ${idea.market}
                </div>
                <div style="background: rgba(255,255,255,0.05); padding: 1rem; border-radius: 10px;">
                    <strong style="color: var(--text-primary);">Difficulty:</strong><br>
                    ${idea.difficulty}
                </div>
                <div style="background: rgba(255,255,255,0.05); padding: 1rem; border-radius: 10px;">
                    <strong style="color: var(--text-primary);">Investment:</strong><br>
                    ${idea.investment}
                </div>
            </div>
        </div>
        
        <div style="margin-bottom: 2rem;">
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">Key Features</h4>
            <ul style="list-style: none; padding: 0;">
                <li style="padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                    <i class="fas fa-check" style="color: #38f9d7; margin-right: 0.5rem;"></i>
                    MVP development in 3-6 months
                </li>
                <li style="padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                    <i class="fas fa-check" style="color: #38f9d7; margin-right: 0.5rem;"></i>
                    Scalable business model
                </li>
                <li style="padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                    <i class="fas fa-check" style="color: #38f9d7; margin-right: 0.5rem;"></i>
                    Strong market demand validation
                </li>
                <li style="padding: 0.5rem 0;">
                    <i class="fas fa-check" style="color: #38f9d7; margin-right: 0.5rem;"></i>
                    Clear monetization strategy
                </li>
            </ul>
        </div>
        
        <div>
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">Next Steps</h4>
            <ol style="color: var(--text-secondary); line-height: 1.8;">
                <li>Conduct user interviews to validate assumptions</li>
                <li>Create wireframes and user journey maps</li>
                <li>Build minimum viable product (MVP)</li>
                <li>Test with early adopters from Reddit communities</li>
                <li>Iterate based on feedback and scale</li>
            </ol>
        </div>
    `;
    
    modal.style.display = 'block';
}

function closeIdeaModal() {
    const modal = document.getElementById('ideaModal');
    modal.style.display = 'none';
}

function validateIdea() {
    showNotification('Starting idea validation process...', 'info');
    
    setTimeout(() => {
        showNotification('Validation complete! Check your email for detailed report.', 'success');
        closeIdeaModal();
    }, 2000);
}

function researchMarket() {
    showNotification('Conducting market research...', 'info');
    
    setTimeout(() => {
        showNotification('Market research completed! Report saved to your dashboard.', 'success');
        closeIdeaModal();
    }, 2500);
}

function saveIdeas() {
    if (currentIdeas.length === 0) {
        showNotification('No ideas to save. Generate some ideas first!', 'error');
        return;
    }
    
    // Simulate saving to local storage or database
    localStorage.setItem('savedIdeas', JSON.stringify(currentIdeas));
    showNotification(`${currentIdeas.length} ideas saved to your collection! 💾`, 'success');
}

function exportIdeas() {
    if (currentIdeas.length === 0) {
        showNotification('No ideas to export. Generate some ideas first!', 'error');
        return;
    }
    
    // Create CSV content
    let csvContent = "Title,Description,Score,Tags,Market,Difficulty,Investment\n";
    currentIdeas.forEach(idea => {
        const row = [
            idea.title,
            idea.description.replace(/,/g, ';'),
            idea.score,
            idea.tags.join(';'),
            idea.market,
            idea.difficulty,
            idea.investment
        ].join(',');
        csvContent += row + "\n";
    });
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'startup_ideas.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    
    showNotification('Ideas exported successfully! 📊', 'success');
}

function updateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    // Update ideas generated count
    if (statNumbers[0]) {
        let currentCount = parseInt(statNumbers[0].textContent.replace(/,/g, ''));
        currentCount += currentIdeas.length;
        statNumbers[0].textContent = currentCount.toLocaleString();
    }
    
    // Animate the numbers
    statNumbers.forEach(stat => {
        stat.style.animation = 'none';
        stat.offsetHeight; // Trigger reflow
        stat.style.animation = 'countUp 0.5s ease';
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    const colors = {
        success: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        error: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        info: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    };
    
    notification.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 15px;
        color: white;
        font-weight: 600;
        z-index: 1001;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        backdrop-filter: blur(20px);
        animation: slideIn 0.3s ease;
        background: ${colors[type]};
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 4000);
}

// Close modal when clicking outside of it
window.onclick = function(event) {
    const modal = document.getElementById('ideaModal');
    if (event.target === modal) {
        closeIdeaModal();
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    @keyframes countUp {
        from {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        to {
            transform: scale(1);
        }
    }
`;
document.head.appendChild(style);

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    // Load saved ideas if any
    const savedIdeas = localStorage.getItem('savedIdeas');
    if (savedIdeas) {
        console.log('Loaded saved ideas:', JSON.parse(savedIdeas));
    }
});