import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../shared'))

from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class ContentGeneratorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="content_generator",
            description="Generates platform-specific social media posts using Gemini API."
        )

    async def execute(self, state: AgentState, platform: str, topic: str) -> ToolResult:
        self.logger.info(f"Generating content for {platform} on topic: {topic}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.GEMINI_API_KEY}"}
                payload = {
                    "model": settings.AGENT_MODEL,
                    "prompt": f"Generate a {platform} post about {topic}. Keep it engaging and under 280 characters."
                }
                async with session.post("https://api.google.com/gemini/v1/generate", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result(data["text"])
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

content_generator_tool = ContentGeneratorTool()