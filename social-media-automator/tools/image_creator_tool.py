import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../shared'))

from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class ImageCreatorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="image_creator",
            description="Creates visuals for social media posts using Replicate API."
        )

    async def execute(self, state: AgentState, topic: str) -> ToolResult:
        self.logger.info(f"Creating image for topic: {topic}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Token {settings.REPLICATE_API_TOKEN}"}
                payload = {
                    "prompt": f"A vibrant illustration of {topic} for social media",
                    "model": "stable-diffusion"
                }
                async with session.post("https://api.replicate.com/v1/predictions", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result(data["output_url"])
                    else:
                        return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

image_creator_tool = ImageCreatorTool()