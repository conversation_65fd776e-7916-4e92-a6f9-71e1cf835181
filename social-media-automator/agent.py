import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../shared'))

import logging
import asyncio
from src.models import AgentState, AgentStatus
from tools.content_generator_tool import content_generator_tool
from tools.image_creator_tool import image_creator_tool

class AgentRunner:
    def __init__(self):
        self.state = AgentState()
        logging.basicConfig(level=logging.INFO)
        
    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")

        try:
            # Step 1: Generate content for X
            content_result = await content_generator_tool.execute(state=self.state, platform="X", topic=self.state.topic)
            if not content_result.success:
                raise Exception(f"Content generation failed: {content_result.error}")
            self.state.research_data["x_content"] = content_result.data
            logging.info("Step 1: X content generated.")

            # Step 2: Create image
            image_result = await image_creator_tool.execute(state=self.state, topic=self.state.topic)
            if not image_result.success:
                raise Exception(f"Image creation failed: {image_result.error}")
            self.state.research_data["image_url"] = image_result.data
            logging.info("Step 2: Image created.")

            self.state.final_output = {"content": self.state.research_data["x_content"], "image": self.state.research_data["image_url"]}
            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")

        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")

        finally:
            self.present_output()
            
    def present_output(self):
        if self.state.status == AgentStatus.COMPLETED:
            print(f"Social Media Content Generated:")
            print(f"Content: {self.state.final_output['content']}")
            print(f"Image URL: {self.state.final_output['image']}")
        else:
            print(f"Agent failed with errors: {self.state.error_log}")

if __name__ == "__main__":
    agent = AgentRunner()
    agent.state.topic = "artificial intelligence trends"
    asyncio.run(agent.run_pipeline())