# 🚀 Social Media Automator

> **Transform Your Social Media Presence with AI-Powered Automation**

Create. Optimize. Automate. Turn your social media management into a streamlined, intelligent workflow that generates engaging content and stunning visuals automatically.

![Social Media Automator](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)
![Python](https://img.shields.io/badge/Python-3.8+-blue)
![License](https://img.shields.io/badge/License-MIT-green)

## ✨ Features

### 🎨 **AI Content Generation**
- **Platform-Specific Posts**: Generate tailored content for X, LinkedIn, Instagram, and Facebook
- **Tone Adaptation**: Professional, casual, humorous, or inspirational content styles  
- **Trending Topics**: Auto-detect and leverage trending topics for maximum engagement
- **Character Optimization**: Perfect post length for each platform's algorithm

### 🖼️ **Intelligent Visual Creation**
- **AI-Generated Images**: Create stunning visuals using Replicate's Stable Diffusion
- **Brand Consistency**: Maintain visual identity across all platforms
- **Dynamic Illustrations**: Context-aware image generation based on content themes
- **Multiple Formats**: Optimized for different social media dimensions

### 📊 **Performance Analytics**
- **Engagement Tracking**: Monitor likes, shares, comments, and reach
- **Content Performance**: Identify top-performing post types and topics
- **Optimal Timing**: AI-suggested best posting times for your audience
- **Growth Metrics**: Track follower growth and engagement rates

### 🔄 **n8n Integration**
- **Workflow Automation**: Seamless integration with n8n for complex workflows
- **API Endpoints**: RESTful API for external integrations
- **Webhook Support**: Real-time triggers and notifications
- **Scheduled Publishing**: Automated content calendar management

## 🎯 Use Cases

- **Small Businesses**: Maintain consistent social media presence without dedicated social media staff
- **Content Creators**: Scale content production across multiple platforms
- **Marketing Agencies**: Manage multiple client accounts efficiently
- **Entrepreneurs**: Bootstrap social media marketing with limited resources

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Gemini API Key
- Replicate API Token

### Installation

1. **Clone and Navigate**
   ```bash
   git clone <repository-url>
   cd social-media-automator
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

4. **Run the Application**
   ```bash
   # Command Line
   python agent.py
   
   # Web Interface
   python -m http.server 8000 --directory web
   # Open http://localhost:8000
   ```

## 🌐 Web Interface

Experience the sleek, vibrant web interface designed for modern social media management:

### 🎨 **Design Features**
- **Dark Mode Interface**: Easy on the eyes with purple/blue gradients
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Real-time Previews**: See your content and images before posting
- **Interactive Analytics**: Visual charts and engagement metrics

### 🛠️ **Interface Components**
- **Content Generator**: Topic input with platform and tone selection
- **Visual Preview**: Real-time content and image preview
- **Action Buttons**: Schedule, edit, and share posts directly
- **Performance Dashboard**: Track your social media growth

### 📱 **Mobile Optimized**
- Touch-friendly interface
- Swipe gestures for navigation
- Optimized loading for mobile networks
- Offline capability for drafts

## ⚙️ Configuration

### Environment Variables
```env
GEMINI_API_KEY=your_gemini_api_key
REPLICATE_API_TOKEN=your_replicate_token
AGENT_MODEL=gemini-pro
LOG_LEVEL=INFO
```

### Content Customization
```python
# Customize content generation
CONTENT_STYLES = {
    'professional': 'formal, business-focused tone',
    'casual': 'friendly, conversational style',
    'humorous': 'witty, engaging with humor',
    'inspirational': 'motivational, uplifting messages'
}
```

## 🔌 API Integration

### Generate Content
```python
POST /generate-content
{
    "topic": "AI trends in 2024",
    "platform": "LinkedIn",
    "tone": "professional"
}
```

### Create Visual
```python
POST /create-image
{
    "topic": "artificial intelligence",
    "style": "vibrant illustration"
}
```

## 📈 Analytics & Insights

- **Content Performance**: Track which topics generate the most engagement
- **Platform Analytics**: Compare performance across different social media platforms
- **Audience Insights**: Understand your audience's preferences and behavior
- **ROI Tracking**: Measure the impact of your social media automation

## 🛡️ Security & Privacy

- **API Key Encryption**: Secure storage of all API credentials
- **Data Privacy**: No content stored permanently on servers
- **Rate Limiting**: Built-in protection against API abuse
- **Error Handling**: Graceful failure recovery and logging

## 🤝 n8n Workflow Integration

### Sample Workflow
1. **Trigger**: Daily cron job or webhook from content calendar
2. **Topic Detection**: Fetch trending topics from Google Trends API
3. **Content Generation**: Call Python agent for content creation
4. **Image Generation**: Create accompanying visuals
5. **Platform Distribution**: Schedule posts across multiple platforms
6. **Analytics Collection**: Gather performance data
7. **Optimization**: Use insights to improve future content

### Deployment Options
- **Serverless**: Deploy as AWS Lambda or Google Cloud Function
- **Container**: Docker deployment for consistent environments
- **Self-hosted**: Run on your own servers for full control

## 🎨 Theme Customization

The web interface features a sleek, modern design with:
- **Color Scheme**: Deep space blues with vibrant purple/pink accents
- **Typography**: Clean Inter font family for excellent readability
- **Animations**: Smooth transitions and hover effects
- **Glassmorphism**: Blurred glass effects for modern aesthetics

## 📊 Success Metrics

Track your success with built-in analytics:
- **Posts Generated**: Total automated content created
- **Engagement Rate**: Average likes, comments, and shares
- **Time Saved**: Hours of manual work automated
- **Growth Rate**: Follower and reach improvements

## 🚀 Advanced Features

- **A/B Testing**: Test different content variations automatically
- **Hashtag Optimization**: AI-powered hashtag suggestions
- **Competitor Analysis**: Monitor and learn from competitor content
- **Content Recycling**: Intelligently repurpose high-performing content

## 🔧 Troubleshooting

### Common Issues
- **API Rate Limits**: Implement exponential backoff retry logic
- **Content Quality**: Adjust prompts and model parameters
- **Image Generation**: Check Replicate API status and quotas

### Support
- Check the logs in `logs/` directory
- Review API key configuration
- Ensure all dependencies are installed correctly

## 📄 License

MIT License - see LICENSE file for details

---

**Ready to revolutionize your social media presence?** Start automating today and watch your engagement soar! 🚀✨