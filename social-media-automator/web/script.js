// Social Media Automator JavaScript
let isGenerating = false;

function generateContent() {
    if (isGenerating) return;
    
    const topic = document.getElementById('topic').value;
    const platform = document.getElementById('platform').value;
    const tone = document.getElementById('tone').value;
    
    if (!topic.trim()) {
        showNotification('Please enter a content topic', 'error');
        return;
    }
    
    isGenerating = true;
    const generateBtn = document.querySelector('.generate-btn');
    const originalText = generateBtn.innerHTML;
    
    // Update button to show loading state
    generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    generateBtn.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';
    
    // Simulate API call
    setTimeout(() => {
        // Mock generated content
        const mockContent = generateMockContent(topic, platform, tone);
        const mockImageUrl = generateMockImage(topic);
        
        // Display results
        displayResults(mockContent, mockImageUrl);
        
        // Reset button
        generateBtn.innerHTML = originalText;
        generateBtn.style.background = 'var(--primary-gradient)';
        isGenerating = false;
        
        showNotification('Content generated successfully!', 'success');
    }, 3000);
}

function generateMockContent(topic, platform, tone) {
    const templates = {
        professional: [
            `Exploring the latest trends in ${topic}. Key insights that could transform your strategy. What's your take? #Innovation #${topic.replace(/\s+/g, '')}`,
            `Breaking down ${topic} for businesses: 3 actionable strategies you can implement today. Thread below 👇 #Business #${topic.replace(/\s+/g, '')}`,
            `The future of ${topic} is here. How are you preparing your organization for these changes? #FutureReady #${topic.replace(/\s+/g, '')}`
        ],
        casual: [
            `Just discovered something cool about ${topic}! 🤯 Had to share this with you all. What do you think? #${topic.replace(/\s+/g, '')} #Cool`,
            `${topic} is getting crazy interesting lately! Anyone else following this? Drop your thoughts below 👇 #${topic.replace(/\s+/g, '')}`,
            `Okay, so ${topic} just blew my mind today 🚀 Here's why you should care too... #MindBlown #${topic.replace(/\s+/g, '')}`
        ],
        humorous: [
            `Me trying to understand ${topic}: 😵‍💫➡️🤓➡️🚀 Anyone else on this journey? #${topic.replace(/\s+/g, '')} #TechLife`,
            `${topic} explained in 3 words: Magic. Pure. Magic. ✨ (Okay that's technically 4 but who's counting?) #${topic.replace(/\s+/g, '')}`,
            `Plot twist: ${topic} is actually taking over the world and we're all here for it 🌍 #${topic.replace(/\s+/g, '')} #PlotTwist`
        ],
        inspirational: [
            `${topic} reminds us that innovation never stops. Every challenge is an opportunity to grow. What's inspiring you today? 💪 #Inspiration #${topic.replace(/\s+/g, '')}`,
            `The journey of ${topic} teaches us: Dream big, start small, move fast. Your next breakthrough awaits! 🌟 #DreamBig #${topic.replace(/\s+/g, '')}`,
            `In a world of ${topic}, be the person who asks 'What if?' and then makes it happen. The future needs your vision! 🚀 #Innovation #${topic.replace(/\s+/g, '')}`
        ]
    };
    
    const toneTemplates = templates[tone] || templates.professional;
    return toneTemplates[Math.floor(Math.random() * toneTemplates.length)];
}

function generateMockImage(topic) {
    // Mock image generation - in real implementation, this would call Replicate API
    return `https://via.placeholder.com/400x300/667eea/ffffff?text=${encodeURIComponent(topic.substring(0, 20))}`;
}

function displayResults(content, imageUrl) {
    const resultsSection = document.getElementById('results');
    const contentPreview = document.getElementById('contentPreview');
    const imagePreview = document.getElementById('imagePreview');
    
    contentPreview.innerHTML = `<p>"${content}"</p>`;
    imagePreview.innerHTML = `<img src="${imageUrl}" alt="Generated Image" style="max-width: 100%; border-radius: 8px;">`;
    
    resultsSection.style.display = 'block';
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'}"></i>
        <span>${message}</span>
    `;
    
    const styles = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        backdrop-filter: blur(20px);
        animation: slideIn 0.3s ease;
        ${type === 'success' 
            ? 'background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);' 
            : 'background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);'
        }
    `;
    
    notification.style.cssText = styles;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Add event listeners for action buttons
document.addEventListener('DOMContentLoaded', function() {
    // Schedule Post button
    document.querySelector('.schedule-btn').addEventListener('click', function() {
        showNotification('Post scheduled successfully! 📅', 'success');
    });
    
    // Edit Content button
    document.querySelector('.edit-btn').addEventListener('click', function() {
        const contentPreview = document.getElementById('contentPreview');
        const currentContent = contentPreview.querySelector('p').textContent.replace(/"/g, '');
        const newContent = prompt('Edit your content:', currentContent);
        if (newContent && newContent.trim()) {
            contentPreview.innerHTML = `<p>"${newContent}"</p>`;
            showNotification('Content updated successfully! ✏️', 'success');
        }
    });
    
    // Share Now button
    document.querySelector('.share-btn').addEventListener('click', function() {
        showNotification('Post shared successfully! 🚀', 'success');
        // Update stats
        updateStats();
    });
});

function updateStats() {
    const statsElements = document.querySelectorAll('.stat-item span');
    if (statsElements.length >= 1) {
        const currentPosts = parseInt(statsElements[0].textContent.match(/\d+/)[0]);
        statsElements[0].textContent = `Posts Generated: ${currentPosts + 1}`;
    }
}